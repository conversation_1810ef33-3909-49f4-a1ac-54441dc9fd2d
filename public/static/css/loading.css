body {
  overflow: hidden;
}
.init-box {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.init-loading {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: space-between;
}
.init-loading-rect {
  background-color: #007aff;
  height: 100%;
  width: 15%;
  animation: init-wave 1.2s infinite ease-in-out;
}

.init-loading-rect:nth-child(1) {
  animation-delay: -1.2s;
}
.init-loading-rect:nth-child(2) {
  animation-delay: -1.1s;
}
.init-loading-rect:nth-child(3) {
  animation-delay: -1s;
}
.init-loading-rect:nth-child(4) {
  animation-delay: -0.9s;
}
.init-loading-rect:nth-child(5) {
  animation-delay: -0.8s;
}

@keyframes init-wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
