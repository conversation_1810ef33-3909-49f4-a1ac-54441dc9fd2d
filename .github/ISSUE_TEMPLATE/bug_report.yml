name: "\U0001F41E Bug 报告"
description: Create a report to help us improve
title: "[Bug] "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        感谢您使用 ContiNew Admin！请您花些时间填写这份 Bug 报告。
  - type: checkboxes
    id: checkboxes
    attributes:
      label: 请您确认
      description: 在提交 Issue 之前，请确保执行过以下操作。
      options:
        - label: 尝试最新 dev 分支代码，仍有相同问题
          required: true
        - label: 阅读[使用指南](https://continew.top/admin/intro/what-is.html)
          required: true
        - label: 查找[常见问题](https://continew.top/faq.html)
          required: true
        - label: 根据报错信息（自行翻译英文）百度或 Google 一下
          required: true
        - label: 搜索是否有其他人提交过类似的 Issue，如果对应 Issue 尚未解决，您可以先订阅关注该 Issue（为了方便后来者查找问题解决方法，请尽量避免创建重复的 Issue）
          required: true
        - label: 阅读源码并在 IDE 中进行断点调试
          required: false
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Bug 描述
      description: 清楚而简洁地描述您遇到的 Bug。另外，非常欢迎您对此 Bug 提交 PR。
      placeholder: 例如：在使用 xxx 功能时出现异常
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: 复现步骤
      description: 条理清晰的步骤或演示视频可以帮助快速定位问题。
      placeholder: 例如：1、xxx; 2、xxx;
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: 预期结果
      description: 清楚而简洁地描述您期望的结果。
      placeholder: 预期结果
    validations:
      required: true
  - type: textarea
    id: environment-info
    attributes:
      label: 环境信息
      description: |
        examples:
          - **ContiNew Admin version(s)**: v3.0.1
      value: "ContiNew Admin version(s):"
      render: markdown
    validations:
      required: true
  - type: textarea
    id: additional-context
    attributes:
      label: 额外补充
      description: 添加您的完整报错信息或屏幕截图，以及一切能帮助定位问题的信息。
