<template>
  <a-select
    :placeholder="placeholder"
    :options="options"
    allow-clear
    allow-search
    style="width: 150px"
  />
</template>

<script setup lang="ts">
import type { LabelValueState } from '@/types/global'

defineOptions({ name: 'TrueAndFalseSelect' })

withDefaults(defineProps<Props>(), {
  placeholder: '请选择',
})

interface Props {
  placeholder?: string
}

const options = ref<LabelValueState[]>([{
  label: '是',
  value: true,
}, {
  label: '否',
  value: false,
}])
</script>

<style scoped lang="less"></style>
