<template>
  <div class="table-page">
    <GiTable
      v-model:selectedKeys="selectedKeys"
      :row-selection="{
        type: 'checkbox',
        showCheckedAll: true,
        onlyCurrent: true,
      }"
      title="BM账号管理"
      columns-cache-key="bmList"
      row-key="id"
      :data="dataList"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%', minWidth: 1000 }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
      @sorter-change="onSorterChange"
    >
      <template #toolbar-left>
        <a-select
          v-model="queryForm.channelId"
          placeholder="请选择关联渠道"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        >
          <a-option
            v-for="item in BM5ChannelList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          />
        </a-select>
        <a-select
          v-model="queryForm.type"
          :options="profitTypeList"
          placeholder="请选择BM类型"
          allow-search
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.status"
          :options="business_manager_status"
          placeholder="请选择状态"
          allow-clear
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.isUse"
          placeholder="是否使用"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.user"
          placeholder="请选择使用者"
          :options="userList"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.isEnterpriseAuth"
          placeholder="企业认证"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isExternal"
          placeholder="外部号"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isRemoveAdmin"
          placeholder="移除管理员"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <a-select
          v-model="queryForm.isBu"
          placeholder="补号"
          allow-clear
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>
        <FUserSelect v-model="queryForm.createUser" placeholder="请选择创建人" @change="search"></FUserSelect>
        <a-input-search
          v-model="queryForm.opsBrowser"
          placeholder="请输入操作号"
          allow-clear
          @search="search"
        />
        <a-input-search
          v-model="queryForm.reserveBrowser"
          placeholder="请输入备用号"
          allow-clear
          @search="search"
        />
        <a-input-search
          v-model="queryForm.reserveBrowserBak"
          placeholder="请输入备用号2"
          allow-clear
          @search="search"
        />
        <a-input-search
          v-model="queryForm.observeBrowser"
          placeholder="请输入观察号"
          allow-clear
          @search="search"
        />
        <a-textarea
          v-model="platformIdInput"
          placeholder="请输入BM5 ID，多个ID可用空格、逗号、换行符分隔"
          allow-clear
          :auto-size="{ minRows: 1, maxRows: 3 }"
          style="width: 300px"
          @change="handlePlatformIdsChange"
        />
        <a-input-search v-model="queryForm.remark" placeholder="备注" allow-clear @search="search" />
        <a-select
          v-model="queryForm.afterSaleStatus"
          placeholder="请选择售后状态"
          :options="pa_after_sale_status"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />

        <a-input-search v-model="queryForm.afterSaleReason" placeholder="售后原因" allow-clear @search="search" />
        <DateRangePicker v-model="queryForm.createTime" :show-time="false" :placeholder="['创建时间', '创建时间']" @change="search" />
        <DateRangePicker v-model="queryForm.useTime" :show-time="false" :placeholder="['使用时间', '使用时间']" @change="search" />
        <DateRangePicker v-model="queryForm.banTime" :show-time="false" :placeholder="['封禁时间', '封禁时间']" @change="search" />

        <a-select
          v-model="queryForm.bannedReason"
          placeholder="请选择封禁状态"
          :options="bm_invalid_reason"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        />
        <a-select
          v-model="queryForm.drop"
          placeholder="掉额"
          allow-clear
          allow-search
          style="width: 150px"
          @change="search"
        >
          <a-option label="是" :value="true"></a-option>
          <a-option label="否" :value="false"></a-option>
        </a-select>

        <a-button @click="reset">
          <template #icon>
            <icon-refresh />
          </template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button v-permission="['biz:businessManager:add']" type="primary" @click="onAdd">
          <template #icon>
            <icon-plus />
          </template>
          <template #default>新增</template>
        </a-button>
        <a-button type="primary" :disabled="selectedKeys.length === 0" @click="copyResult">
          复制售后信息
        </a-button>
        <a-button v-permission="['biz:businessManager:export']" @click="onExport">
          <template #icon>
            <icon-download />
          </template>
          <template #default>导出</template>
        </a-button>

        <a-button
          @click="onChannelStat"
        >
          <template #icon><icon-bar-chart /></template>
          渠道分析
        </a-button>

        <a-button
          @click="onBanStat"
        >
          <template #icon><icon-bar-chart /></template>
          封禁分析
        </a-button>
      </template>

      <template #status="{ record }">
        <GiCellTag :value="record.status" :dict="business_manager_status" />
      </template>
      <template #browser="{ record }">
        <AdsBrowser :browser-no="['', '', record.opsBrowser, record.reserveBrowser, record.reserveBrowserBak, record.observeBrowser].join(',')" :open-url="getBusinessManagerMainPage(record.platformId)" :status="0"></AdsBrowser>
      </template>
      <template #channelId="{ record }">
        {{ getBM5(record.channelId) }}
      </template>
      <template #isUse="{ record }">
        <a-tag v-if="record.isUse" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #isBu="{ record }">
        <a-tag v-if="record.isBu" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #tag="{ record }">
        <a-space size="mini" wrap>
          <a-tag v-if="record.isExternal" color="gray" size="small">外部号</a-tag>
          <a-tag v-if="record.isEnterpriseAuth" color="gray" size="small">企业认证</a-tag>
          <a-tag v-if="!record.isRemoveAdmin" color="red">未移除管理员</a-tag>
        </a-space>
      </template>
      <template #afterSaleStatus="{ record }">
        <GiCellTag :value="record.afterSaleStatus" :dict="pa_after_sale_status" />
      </template>
      <template #bannedReason="{ record }">
        <GiCellTag :value="record.bannedReason" :dict="bm_invalid_reason" />
      </template>
      <template #num="{ record }">
        {{ record.useItemNum }} / {{ record.num }}
      </template>
      <template #drop="{ record }">
        <a-tag v-if="record.drop" color="arcoblue" size="small">是</a-tag>
        <a-tag v-else color="red" size="small">否</a-tag>
      </template>
      <template #action="{ record }">
        <a-space wrap>
          <a-link v-permission="['biz:businessManager:update']" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link v-if="!record.isUse" v-permission="['biz:businessManager:update']" title="已使用" @click="onUsed(record.id)">已使用</a-link>
          <a-link v-if="record.status === 1" v-permission="['biz:businessManager:update']" title="封禁" @click="onBan(record.id)">封禁</a-link>
          <a-link v-permission="['biz:businessManager:update']" title="新增坑位" @click="onAddItem(record)">新增坑位</a-link>
          <a-link
            v-permission="['biz:businessManager:delete']"
            status="danger"
            :disabled="record.disabled"
            :title="record.disabled ? '不可删除' : '删除'"
            @click="onDelete(record)"
          >
            删除
          </a-link>
        </a-space>
      </template>
    </GiTable>

    <BusinessManagerAddModal ref="BusinessManagerAddModalRef" @save-success="search" />
    <BusinessManagerDetailDrawer ref="BusinessManagerDetailDrawerRef" />
    <BusinessManagerAddItemModal ref="BusinessManagerAddItemRef" @save-success="search" />
    <BusinessManagerChannelStatModal ref="BusinessManagerChannelStatModalRef" />

    <BusinessManagerBanStatModal ref="BusinessManagerBanStatModalRef" />

    <!-- 封禁确认弹窗 -->
    <a-modal
      v-model:visible="banModalVisible"
      title="提示"
      :mask-closable="false"
      :ok-button-props="{ status: 'danger' }"
      @ok="handleBanConfirm"
      @cancel="handleBanCancel"
    >
      <p>封禁BM的同时会封禁名下的坑位和广告户并进行提现, 是否确定封禁该条数据?</p>
      <a-form ref="banFormRef" :model="banForm" style="margin-top: 15px">
        <a-form-item field="reason" label="封禁原因">
          <a-select
            v-model="banForm.reason"
            placeholder="请选择封禁原因"
            :options="bm_invalid_reason"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import BusinessManagerAddModal from './BusinessManagerAddModal.vue'
import BusinessManagerDetailDrawer from './BusinessManagerDetailDrawer.vue'
import BusinessManagerBanStatModal from './BusinessManagerBanStatModal.vue'
import {
  type BusinessManagerQuery,
  type BusinessManagerResp,
  deleteBusinessManager,
  exportBusinessManager,
  listBusinessManager,
  setBusinessManagerBan,
  setBusinessManagerUsed,
} from '@/apis/biz/businessManager'
import type { TableInstanceColumns } from '@/components/GiTable/type'
import { useDownload, useTable } from '@/hooks'
import { getBusinessManagerMainPage, isMobile } from '@/utils'
import has from '@/utils/has'
import { getBusinessManagerChannelList } from '@/apis/biz/businessManagerChannel'
import { useDict } from '@/hooks/app'
import { listProfitTypeDict, listUserDict } from '@/apis'
import type { LabelValueState } from '@/types/global'
import FUserSelect from '@/components/FUserSelect/index.vue'
import BusinessManagerAddItemModal from '@/views/biz/businessManager/BusinessManagerAddItemModal.vue'
import BusinessManagerChannelStatModal from '@/views/biz/businessManager/BusinessManagerChannelStatModal.vue'

defineOptions({ name: 'BusinessManager' })

// 添加用户列表状态
const userList = ref<LabelValueState[]>([])

// 添加获取用户列表方法
const getUserList = async () => {
  const { data } = await listUserDict()
  userList.value = data.map((item) => ({
    ...item,
    value: item.label,
  }))
}

const { business_manager_status, pa_after_sale_status, bm_invalid_reason } = useDict('business_manager_status', 'pa_after_sale_status', 'bm_invalid_reason')

const queryForm = reactive<BusinessManagerQuery>({
  channelId: undefined,
  platformIds: undefined,
  remark: undefined,
  status: undefined,
  isUse: undefined,
  sort: ['createTime,desc'],
  createTime: undefined,
  browserNo: undefined,
  user: undefined,
  createUser: undefined,
  useTime: undefined,
  banTime: undefined,
  opsBrowser: undefined,
  reserveBrowser: undefined,
  observeBrowser: undefined,
  afterSaleStatus: undefined,
  bannedReason: undefined,
  afterSaleReason: undefined,
  type: undefined,
  isExternal: undefined,
  isEnterpriseAuth: undefined,
  isRemoveAdmin: undefined,
  reserveBrowserBak: undefined,
  drop: undefined,
  isBu: undefined,
})

const profitTypeList = ref<LabelValueState[]>([])

const getProfitTypeList = async () => {
  const { data } = await listProfitTypeDict({ adPlatform: 1, cat: 'ad_account' })
  profitTypeList.value = data
}

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  refresh,
  handleDelete,
  selectedKeys,
} = useTable((page) => listBusinessManager({ ...queryForm, ...page }), { immediate: true })
const columns = ref<TableInstanceColumns[]>([
  {
    title: '序号',
    width: 66,
    align: 'center',
    render: ({ rowIndex }) => h('span', {}, rowIndex + 1 + (pagination.current - 1) * pagination.pageSize),
    fixed: !isMobile() ? 'left' : undefined,
  },
  { title: '关联渠道', dataIndex: 'channelId', slotName: 'channelId', width: 120 },
  { title: 'BM ID', dataIndex: 'platformId', slotName: 'platformId', width: 180 },
  { title: '类型', dataIndex: 'typeName', slotName: 'typeName', width: 80, align: 'center' },
  { title: '掉额', dataIndex: 'drop', slotName: 'drop', width: 80, align: 'center' },
  { title: '标签', dataIndex: 'tag', slotName: 'tag', width: 160, align: 'center' },
  { title: '操作号', dataIndex: 'opsBrowser', slotName: 'opsBrowser', align: 'center', width: 80, tooltip: true, ellipsis: true },
  { title: '备用号', dataIndex: 'reserveBrowser', slotName: 'reserveBrowser', align: 'center', width: 80, tooltip: true, ellipsis: true },
  { title: '备用号2', dataIndex: 'reserveBrowserBak', slotName: 'reserveBrowserBak', align: 'center', width: 100, tooltip: true, ellipsis: true },
  { title: '观察号', dataIndex: 'observeBrowser', slotName: 'observeBrowser', align: 'center', width: 80, tooltip: true, ellipsis: true },
  { title: '浏览器', dataIndex: 'browser', slotName: 'browser', align: 'center', width: 100 },
  { title: '账号信息', dataIndex: 'content', slotName: 'content', width: 240 },
  { title: '坑位', dataIndex: 'num', slotName: 'num', align: 'center', width: 100 },
  { title: '拉户数', dataIndex: 'useNum', slotName: 'useNum', align: 'center', width: 100 },
  { title: '大黑号数', dataIndex: 'useBlackNum', slotName: 'useBlackNum', align: 'center', width: 120, sortable: {
    sortDirections: ['ascend', 'descend'],
    sorter: true,
  } },
  { title: '单价', dataIndex: 'unitPrice', slotName: 'unitPrice', align: 'center', width: 100 },
  { title: '状态', dataIndex: 'status', slotName: 'status', align: 'center', width: 80 },
  { title: '是否使用', dataIndex: 'isUse', slotName: 'isUse', align: 'center', width: 100 },
  { title: '是否补号', dataIndex: 'isBu', slotName: 'isBu', align: 'center', width: 100 },
  { title: '使用者', dataIndex: 'user', slotName: 'user', width: 100 },
  { title: '使用时间', dataIndex: 'useTime', slotName: 'useTime', width: 180 },
  { title: '售后状态', dataIndex: 'afterSaleStatus', slotName: 'afterSaleStatus', width: 100, align: 'center' },
  { title: '售后原因', dataIndex: 'afterSaleReason', slotName: 'afterSaleReason', width: 180, tooltip: true, ellipsis: true },
  { title: '封禁原因', dataIndex: 'bannedReason', slotName: 'bannedReason', width: 180, tooltip: true, ellipsis: true },
  { title: '封禁时间', dataIndex: 'banTime', slotName: 'banTime', width: 180 },
  { title: '备注', dataIndex: 'remark', slotName: 'remark', tooltip: true, ellipsis: true, width: 140 },
  { title: '创建人', dataIndex: 'createUserString', slotName: 'createUserString', width: 120 },
  { title: '创建时间', dataIndex: 'createTime', slotName: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 220,
    align: 'center',
    fixed: !isMobile() ? 'right' : undefined,
    show: has.hasPermOr(['biz:businessManager:detail', 'biz:businessManager:update', 'biz:businessManager:delete']),
  },
])

// 重置
const reset = () => {
  queryForm.channelId = undefined
  queryForm.platformIds = undefined
  queryForm.browserNo = undefined
  queryForm.user = undefined
  queryForm.remark = undefined
  queryForm.status = undefined
  queryForm.isUse = undefined
  queryForm.afterSaleStatus = undefined
  queryForm.banTime = undefined
  queryForm.isBu = undefined
  queryForm.reserveBrowser = undefined
  queryForm.isExternal = undefined
  queryForm.isEnterpriseAuth = undefined
  queryForm.isRemoveAdmin = undefined
  queryForm.afterSaleReason = undefined
  queryForm.bannedReason = undefined
  queryForm.createTime = undefined
  queryForm.createUser = undefined
  queryForm.reserveBrowserBak = undefined
  queryForm.observeBrowser = undefined
  queryForm.opsBrowser = undefined
  queryForm.type = undefined
  queryForm.user = undefined
  queryForm.useTime = undefined
  search()
}

// 删除
const onDelete = (record: BusinessManagerResp) => {
  return handleDelete(() => deleteBusinessManager(record.id), {
    content: `是否确定删除该条数据？`,
    showModal: true,
  })
}

// 导出
const onExport = () => {
  useDownload(() => exportBusinessManager(queryForm))
}

const BusinessManagerAddModalRef = ref<InstanceType<typeof BusinessManagerAddModal>>()
// 新增
const onAdd = () => {
  BusinessManagerAddModalRef.value?.onAdd()
}

// 修改
const onUpdate = (record: BusinessManagerResp) => {
  BusinessManagerAddModalRef.value?.onUpdate(record.id)
}

const BusinessManagerDetailDrawerRef = ref<InstanceType<typeof BusinessManagerDetailDrawer>>()

const BM5ChannelList = ref<any[]>([])
const getBM5Channel = async () => {
  const res = await getBusinessManagerChannelList()
  BM5ChannelList.value = res.data
}
const getBM5 = (id: number | string) => {
  const find = BM5ChannelList.value.find((item: any) => item.id === id)
  return find ? find.name : ''
}
getBM5Channel()
// 修改
const onUsed = async (id: string) => {
  await setBusinessManagerUsed(id)
  Message.success('修改成功')
  refresh()
}

// 封禁
// 添加封禁相关的响应式变量
const banModalVisible = ref(false)
const banFormRef = ref()
const banForm = reactive({
  reason: undefined,
  id: undefined,
})

// 修改封禁方法
const onBan = async (id: string) => {
  banForm.id = id
  banForm.reason = undefined
  banModalVisible.value = true
}

// 添加确认和取消处理方法
const handleBanConfirm = async () => {
  try {
    await banFormRef.value.validate()
    await setBusinessManagerBan(banForm.id, banForm.reason)
    Message.success('封禁成功')
    banModalVisible.value = false
    refresh()
  } catch (error) {
    // 表单验证失败会进入这里
    console.error(error)
  }
}

const handleBanCancel = () => {
  banModalVisible.value = false
  banForm.reason = undefined
  banForm.id = undefined
}

const platformIdInput = ref('')

const handlePlatformIdsChange = () => {
  const ids = platformIdInput.value
    .split(/[\s,]+/)
    .map((id) => id.trim())
    .filter((id) => id)
  queryForm.platformIds = ids.length > 0 ? ids.join(',') : undefined
  search()
}

const copyResult = async () => {
  let text = ''
  dataList.value.forEach((item) => {
    if (selectedKeys.value.includes(item.id)) {
      text += `${item.content} ${item.afterSaleReason}\n`
    }
  })
  try {
    await navigator.clipboard.writeText(text)
    Message.success('复制成功')
  } catch (err) {
    Message.error('复制失败')
  }
}

const BusinessManagerAddItemRef = ref<InstanceType<typeof BusinessManagerAddItemModal>>()
const onAddItem = (record: BusinessManagerResp) => {
  BusinessManagerAddItemRef.value?.onAdd(record)
}

getUserList()

const BusinessManagerChannelStatModalRef = ref<InstanceType<typeof BusinessManagerChannelStatModal>>()

const onChannelStat = () => {
  BusinessManagerChannelStatModalRef.value?.onOpen()
}

const BusinessManagerBanStatModalRef = ref<InstanceType<typeof BusinessManagerBanStatModal>>()

const onBanStat = () => {
  BusinessManagerBanStatModalRef.value?.onOpen()
}

const onSorterChange = (dataIndex: string, direction: string) => {
  let defaultSort = ['createTime,desc']
  if (direction) {
    defaultSort = [dataIndex, direction === 'descend' ? 'desc' : 'asc']
  }
  queryForm.sort = defaultSort
  search()
}
getProfitTypeList()
</script>

<style scoped lang="scss"></style>
