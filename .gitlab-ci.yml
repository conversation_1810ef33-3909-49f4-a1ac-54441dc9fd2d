image: node:18

stages:
  - build
  - deploy

before_script:
  - pnpm i

build:
  stage: build
  script:
    - pnpm build
  artifacts:
    paths:
      - dist/
  when: manual

deploy:
  stage: deploy
  needs: ["build"]
  script:
    - echo "$SSH_PRIVATE_KEY" > private_key
    - chmod 600 private_key
    - rsync -avz -e "ssh -i private_key -o StrictHostKeyChecking=no" dist/* $SSH_USER@$SERVER_IP:/opt/facebook/web/dist
  only:
    - master